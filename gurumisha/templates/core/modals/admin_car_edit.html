{% load static %}

<!-- Edit Car Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto"
     id="edit-car-modal"
     x-data="{ show: false }"
     x-init="show = true"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">

    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity modal-backdrop"
         @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"></div>

    <!-- Modal Container -->
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <!-- Modal Panel -->
        <div class="modal-panel inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
             x-show="show"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-harrier-red to-harrier-dark px-6 py-5 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-white bg-opacity-20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4 shadow-lg">
                            <i class="fas fa-edit text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white font-montserrat">
                                Edit Car Listing
                            </h3>
                            <p class="text-sm text-white text-opacity-90 font-raleway">Modify car details and approval status</p>
                        </div>
                    </div>
                    <button type="button"
                            class="text-white hover:text-gray-200 transition-colors p-2 hover:bg-white hover:bg-opacity-10 rounded-lg"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- Modal Body -->
            <div class="bg-gradient-to-br from-gray-50 to-white px-6 py-6 max-h-96 overflow-y-auto modal-body">
                <form id="edit-car-form"
                      hx-post="{% url 'core:admin_car_edit' car.id %}"
                      hx-target="body"
                      hx-on::after-request="handleEditResponse(event)"
                      hx-on::before-request="handleEditBefore(event)"
                      class="space-y-6"
                      x-data="{ isSubmitting: false }"
                      @submit="isSubmitting = true; validateHotDealFields(event)">
                    {% csrf_token %}

                    <!-- Basic Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-tag text-harrier-red mr-2"></i>Title
                            </label>
                            {{ form.title }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-dollar-sign text-harrier-red mr-2"></i>Price (KSH)
                            </label>
                            {{ form.price }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-car text-harrier-red mr-2"></i>Brand
                            </label>
                            {{ form.brand }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-car-side text-harrier-red mr-2"></i>Model
                            </label>
                            {{ form.model }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-calendar text-harrier-red mr-2"></i>Year
                            </label>
                            {{ form.year }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-palette text-harrier-red mr-2"></i>Color
                            </label>
                            {{ form.color }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-cog text-harrier-red mr-2"></i>Condition
                            </label>
                            {{ form.condition }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-tachometer-alt text-harrier-red mr-2"></i>Mileage (km)
                            </label>
                            {{ form.mileage }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-gas-pump text-harrier-red mr-2"></i>Fuel Type
                            </label>
                            {{ form.fuel_type }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-cogs text-harrier-red mr-2"></i>Transmission
                            </label>
                            {{ form.transmission }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-engine text-harrier-red mr-2"></i>Engine Size (L)
                            </label>
                            {{ form.engine_size }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-list text-harrier-red mr-2"></i>Listing Type
                            </label>
                            {{ form.listing_type }}
                        </div>
                    </div>

                    <!-- Description and Features -->
                    <div class="space-y-6">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-file-alt text-harrier-red mr-2"></i>Description
                            </label>
                            {{ form.description }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-list-check text-harrier-red mr-2"></i>Features (comma separated)
                            </label>
                            {{ form.features }}
                        </div>
                    </div>

                    <!-- Status and Approval -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-flag text-harrier-red mr-2"></i>Status
                            </label>
                            {{ form.status }}
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-star text-harrier-red mr-2"></i>Star Rating (0-5)
                            </label>
                            {{ form.star_rating }}
                            <p class="text-xs text-gray-500 mt-1">Higher ratings get better visibility (4+ stars = Featured)</p>
                        </div>
                    </div>

                    <!-- Checkboxes Section -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-cog text-harrier-red"></i>Options & Settings
                            </label>
                            <div class="space-y-4 bg-gradient-to-br from-gray-50 to-white p-6 rounded-2xl border border-gray-200">
                                <div class="checkbox-group">
                                    {{ form.is_approved }}
                                    <label for="{{ form.is_approved.id_for_label }}" class="text-sm font-semibold text-gray-700">
                                        <i class="fas fa-check-circle text-green-500 mr-2"></i>Approved for Public Listing
                                    </label>
                                </div>
                                <div class="checkbox-group">
                                    {{ form.negotiable }}
                                    <label for="{{ form.negotiable.id_for_label }}" class="text-sm font-semibold text-gray-700">
                                        <i class="fas fa-handshake text-blue-500 mr-2"></i>Price is Negotiable
                                    </label>
                                </div>
                                <div class="checkbox-group">
                                    {{ form.is_hot_deal }}
                                    <label for="{{ form.is_hot_deal.id_for_label }}" class="text-sm font-semibold text-gray-700">
                                        <i class="fas fa-fire text-red-500 mr-2"></i>Mark as Hot Deal
                                    </label>
                                </div>
                                <div class="checkbox-group">
                                    {{ form.is_featured }}
                                    <label for="{{ form.is_featured.id_for_label }}" class="text-sm font-semibold text-gray-700">
                                        <i class="fas fa-star text-purple-500 mr-2"></i>Mark as Featured
                                    </label>
                                </div>
                                <div class="checkbox-group">
                                    {{ form.is_certified }}
                                    <label for="{{ form.is_certified.id_for_label }}" class="text-sm font-semibold text-gray-700">
                                        <i class="fas fa-certificate text-green-500 mr-2"></i>Mark as Certified
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hot Deals Dynamic Form Section -->
                    <div id="hot-deals-section"
                         class="form-group transition-all duration-500 ease-in-out transform"
                         style="display: {% if form.is_hot_deal.value %}block{% else %}none{% endif %};"
                         x-data="{ isVisible: {% if form.is_hot_deal.value %}true{% else %}false{% endif %} }"
                         x-show="isVisible"
                         x-transition:enter="transition ease-out duration-300"
                         x-transition:enter-start="opacity-0 transform scale-95"
                         x-transition:enter-end="opacity-100 transform scale-100"
                         x-transition:leave="transition ease-in duration-200"
                         x-transition:leave-start="opacity-100 transform scale-100"
                         x-transition:leave-end="opacity-0 transform scale-95">

                        <label class="form-label flex items-center mb-4">
                            <div class="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full mr-3 shadow-lg">
                                <i class="fas fa-fire text-white text-sm"></i>
                            </div>
                            <span class="text-lg font-bold bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
                                Hot Deal Configuration
                            </span>
                        </label>

                        <div class="relative bg-gradient-to-br from-red-50/80 to-orange-50/80 backdrop-blur-sm p-8 rounded-3xl border border-red-200/50 shadow-xl hover:shadow-2xl transition-all duration-300">
                            <!-- Glassmorphism overlay -->
                            <div class="absolute inset-0 bg-white/20 backdrop-blur-sm rounded-3xl"></div>

                            <!-- Content -->
                            <div class="relative z-10">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                                    <!-- Discount Percentage -->
                                    <div class="form-group">
                                        <label class="text-sm font-bold text-gray-800 mb-3 block flex items-center">
                                            <div class="flex items-center justify-center w-6 h-6 bg-red-500 rounded-full mr-2">
                                                <i class="fas fa-percentage text-white text-xs"></i>
                                            </div>
                                            Discount Percentage
                                        </label>
                                        <div class="relative">
                                            <input type="number"
                                                   name="hot_deal_discount"
                                                   id="hot_deal_discount"
                                                   class="form-input bg-white/80 backdrop-blur-sm border-red-200 focus:border-red-400 focus:ring-red-400/30 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl"
                                                   min="5"
                                                   max="50"
                                                   step="1"
                                                   value="{% if car.hot_deal_details %}{{ car.hot_deal_details.discount_value }}{% else %}10{% endif %}"
                                                   placeholder="Enter discount percentage (5-50%)"
                                                   onchange="calculateDiscountedPrice()">
                                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                                <span class="text-red-500 font-bold">%</span>
                                            </div>
                                        </div>
                                        <p class="text-xs text-gray-600 mt-2 bg-white/50 rounded-lg px-3 py-1">
                                            <i class="fas fa-lightbulb text-yellow-500 mr-1"></i>
                                            Suggested: 10-25% for regular deals, 30-50% for clearance
                                        </p>
                                    </div>

                                    <!-- Days Remaining -->
                                    <div class="form-group">
                                        <label class="text-sm font-bold text-gray-800 mb-3 block flex items-center">
                                            <div class="flex items-center justify-center w-6 h-6 bg-orange-500 rounded-full mr-2">
                                                <i class="fas fa-calendar-alt text-white text-xs"></i>
                                            </div>
                                            Days Remaining
                                        </label>
                                        <div class="relative">
                                            <input type="number"
                                                   name="hot_deal_days"
                                                   id="hot_deal_days"
                                                   class="form-input bg-white/80 backdrop-blur-sm border-orange-200 focus:border-orange-400 focus:ring-orange-400/30 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl"
                                                   min="1"
                                                   max="30"
                                                   step="1"
                                                   value="{% if car.hot_deal_details %}{% with days_remaining=car.hot_deal_details.days_remaining %}{% if days_remaining > 0 %}{{ days_remaining }}{% else %}7{% endif %}{% endwith %}{% else %}7{% endif %}"
                                                   placeholder="Enter number of days (1-30)"
                                                   onchange="calculateExpiryDate()">
                                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                                <span class="text-orange-500 font-bold">days</span>
                                            </div>
                                        </div>
                                        <p class="text-xs text-gray-600 mt-2 bg-white/50 rounded-lg px-3 py-1">
                                            <i class="fas fa-clock text-blue-500 mr-1"></i>
                                            Hot deal duration (recommended: 3-14 days)
                                        </p>
                                    </div>

                                    <!-- Discounted Price Display -->
                                    <div class="form-group">
                                        <label class="text-sm font-bold text-gray-800 mb-3 block flex items-center">
                                            <div class="flex items-center justify-center w-6 h-6 bg-green-500 rounded-full mr-2">
                                                <i class="fas fa-tag text-white text-xs"></i>
                                            </div>
                                            Discounted Price
                                        </label>
                                        <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-xl border border-green-200 shadow-lg backdrop-blur-sm">
                                            <div class="flex items-center justify-between">
                                                <span id="discounted-price-display" class="text-2xl font-bold text-green-600 tracking-tight">
                                                    KSh {{ car.price|floatformat:0 }}
                                                </span>
                                                <div class="flex items-center space-x-2">
                                                    <span class="px-2 py-1 bg-green-500 text-white text-xs font-bold rounded-full">SAVE</span>
                                                    <span id="savings-amount" class="text-sm font-semibold text-green-700"></span>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-600 mt-2 flex items-center">
                                                <i class="fas fa-calculator text-green-500 mr-1"></i>
                                                Automatically calculated based on discount
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Expiry Date Display -->
                                    <div class="form-group">
                                        <label class="text-sm font-bold text-gray-800 mb-3 block flex items-center">
                                            <div class="flex items-center justify-center w-6 h-6 bg-blue-500 rounded-full mr-2">
                                                <i class="fas fa-clock text-white text-xs"></i>
                                            </div>
                                            Deal Expires On
                                        </label>
                                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200 shadow-lg backdrop-blur-sm">
                                            <div class="flex items-center justify-between">
                                                <span id="expiry-date-display" class="text-lg font-bold text-blue-600">
                                                    <!-- Will be calculated by JavaScript -->
                                                </span>
                                                <div class="flex items-center space-x-1">
                                                    <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                                    <span class="text-xs font-semibold text-blue-600">ACTIVE</span>
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-600 mt-2 flex items-center">
                                                <i class="fas fa-hourglass-half text-blue-500 mr-1"></i>
                                                Automatically calculated from days remaining
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center mt-6 pt-6 border-t border-gray-200">
                        <!-- Cancel Button -->
                        <button type="button"
                                class="enhanced-btn enhanced-btn-cancel"
                                @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                            <i class="fas fa-times mr-2"></i>
                            <span>Cancel</span>
                        </button>

                        <!-- Submit Button -->
                        <button type="submit"
                                class="enhanced-btn enhanced-btn-submit"
                                :disabled="isSubmitting">
                            <i class="fas fa-save mr-2" x-show="!isSubmitting"></i>
                            <i class="fas fa-spinner fa-spin mr-2" x-show="isSubmitting"></i>
                            <span x-text="isSubmitting ? 'Saving Changes...' : 'Save Changes'"></span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Enhanced Modal Footer -->
            <div class="bg-gradient-to-r from-gray-50 via-white to-gray-50 px-6 py-6 border-t border-gray-200">
                <!-- Info Section -->
                <div class="flex items-center justify-center mb-6">
                    <div class="flex items-center text-sm text-gray-600 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
                        <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-info text-white text-xs"></i>
                        </div>
                        <span class="font-medium font-raleway">Changes will be saved immediately upon submission</span>
                    </div>
                </div>

                <!-- Additional Info -->
                <div class="mt-4 text-center">
                    <p class="text-xs text-gray-500 font-raleway">
                        Vendor will be notified if approval status changes
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Enhanced Modal Styles */
    .modal-panel {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* Enhanced Form Styling */
    .form-group {
        @apply space-y-3 mb-6;
        position: relative;
    }

    .form-label {
        @apply block text-sm font-bold text-gray-800 flex items-center mb-2;
        font-family: 'Montserrat', sans-serif;
        letter-spacing: 0.025em;
        position: relative;
    }

    .form-label i {
        margin-right: 8px;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
    }

    .form-input {
        @apply w-full border-2 rounded-2xl transition-all duration-300 ease-out;
        font-family: 'Inter', 'Raleway', sans-serif;
        font-size: 15px;
        font-weight: 500;
        line-height: 1.5;
        padding: 16px 20px;

        /* Modern glassmorphism background */
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(12px);
        border: 2px solid rgba(226, 232, 240, 0.8);

        /* Subtle shadow */
        box-shadow:
            0 1px 3px rgba(0, 0, 0, 0.05),
            0 0 0 1px rgba(255, 255, 255, 0.05) inset;

        /* Text styling */
        color: #1e293b;
        letter-spacing: 0.01em;
    }

    .form-input:hover {
        border-color: rgba(220, 38, 38, 0.3);
        background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(254, 242, 242, 0.95) 100%);
        box-shadow:
            0 2px 8px rgba(220, 38, 38, 0.08),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        transform: translateY(-1px);
    }

    .form-input:focus {
        outline: none;
        border-color: #dc2626;
        background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(254, 242, 242, 0.98) 100%);
        box-shadow:
            0 4px 20px rgba(220, 38, 38, 0.15),
            0 0 0 4px rgba(220, 38, 38, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.2) inset;
        transform: translateY(-2px);
    }

    .form-input::placeholder {
        color: #94a3b8;
        font-weight: 400;
        opacity: 0.8;
    }

    /* Select specific styling */
    select.form-input {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23dc2626' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 16px center;
        background-repeat: no-repeat;
        background-size: 16px 12px;
        padding-right: 48px;
        cursor: pointer;
    }

    select.form-input:focus {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23b91c1c' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    }

    /* Textarea specific styling */
    textarea.form-input {
        resize: vertical;
        min-height: 120px;
        line-height: 1.6;
        padding: 16px 20px;
    }

    /* Checkbox styling */
    input[type="checkbox"].form-input {
        width: 20px;
        height: 20px;
        border-radius: 6px;
        padding: 0;
        margin: 0;
        cursor: pointer;
        position: relative;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 2px solid #e2e8f0;
        transition: all 0.2s ease;
    }

    input[type="checkbox"].form-input:checked {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        border-color: #dc2626;
        transform: scale(1.05);
    }

    input[type="checkbox"].form-input:checked::before {
        content: "✓";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    input[type="checkbox"].form-input:focus {
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.2);
        transform: translateY(0);
    }

    /* Number input styling */
    input[type="number"].form-input {
        -moz-appearance: textfield;
    }

    input[type="number"].form-input::-webkit-outer-spin-button,
    input[type="number"].form-input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Error state styling */
    .form-input.error,
    .has-error .form-input {
        border-color: #ef4444;
        background: linear-gradient(135deg, rgba(254, 242, 242, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
        box-shadow:
            0 2px 8px rgba(239, 68, 68, 0.15),
            0 0 0 1px rgba(239, 68, 68, 0.1) inset;
    }

    .form-input.error:focus,
    .has-error .form-input:focus {
        border-color: #dc2626;
        box-shadow:
            0 4px 20px rgba(239, 68, 68, 0.2),
            0 0 0 4px rgba(239, 68, 68, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.2) inset;
    }

    /* Success state styling */
    .form-input.success,
    .has-success .form-input {
        border-color: #22c55e;
        background: linear-gradient(135deg, rgba(240, 253, 244, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    }

    /* Disabled state styling */
    .form-input:disabled {
        background: linear-gradient(135deg, rgba(248, 250, 252, 0.6) 0%, rgba(241, 245, 249, 0.6) 100%);
        border-color: #e2e8f0;
        color: #94a3b8;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    /* Input group styling for checkboxes */
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 0;
    }

    .checkbox-group label {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    /* Enhanced Button Base Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        max-width: 180px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: visible;
        white-space: nowrap;
        box-sizing: border-box;
    }

    /* Simplified Cancel Button */
    .enhanced-btn-cancel {
        background: linear-gradient(135deg, #f9fafb, #f3f4f6);
        color: #374151;
        border: 2px solid #e5e7eb;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .enhanced-btn-cancel:hover {
        background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
        color: #1f2937;
        border-color: #d1d5db;
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    }

    /* Simplified Submit Button */
    .enhanced-btn-submit {
        background: linear-gradient(135deg, #dc2626, #ef4444, #b91c1c);
        background-size: 200% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 24px rgba(220, 38, 38, 0.4);
    }

    .enhanced-btn-submit:hover {
        background-position: 100% 50%;
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(220, 38, 38, 0.5);
    }

    .enhanced-btn-submit:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
    }
</style>

<script>
// Handle edit form response
function handleEditResponse(event) {
    const xhr = event.detail.xhr;

    console.log('Edit response received:', {
        status: xhr.status,
        responseText: xhr.responseText.substring(0, 200) + '...'
    });

    if (xhr.status === 200) {
        try {
            const response = JSON.parse(xhr.responseText);
            console.log('Parsed response:', response);

            if (response.status === 'success') {
                // Show success notification
                if (window.toastManager) {
                    window.toastManager.show(response.message, 'success', {
                        duration: 5000,
                        action: {
                            text: 'View Car',
                            handler: () => {
                                window.location.href = `/dashboard/admin/car/${response.car_id}/`;
                            }
                        }
                    });
                }

                // Close modal
                const modal = document.getElementById('edit-car-modal');
                if (modal) {
                    modal.style.display = 'none';
                    setTimeout(() => modal.remove(), 300);
                }

                // Reload page to show changes
                if (response.reload_required) {
                    setTimeout(() => location.reload(), 1000);
                }
            } else {
                console.error('Edit failed:', response);
                if (window.toastManager) {
                    window.toastManager.show(response.message || 'Edit failed', 'error', {
                        duration: 5000
                    });
                }
            }
        } catch (e) {
            // If not JSON, it's probably the form with errors
            console.log('Form returned with validation errors:', e);
            console.log('Response text:', xhr.responseText);
        }
    } else {
        console.error('HTTP error:', xhr.status, xhr.responseText);
        if (window.toastManager) {
            window.toastManager.show('An error occurred while saving changes', 'error', {
                duration: 5000
            });
        }
    }
}

// Hot Deals Dynamic Form Functions with Enhanced Validation
function calculateDiscountedPrice() {
    const priceInput = document.querySelector('#id_price');
    const discountInput = document.querySelector('#hot_deal_discount');
    const discountedPriceDisplay = document.querySelector('#discounted-price-display');
    const savingsAmountDisplay = document.querySelector('#savings-amount');

    if (priceInput && discountInput && discountedPriceDisplay) {
        const originalPrice = parseFloat(priceInput.value) || 0;
        const discountPercent = parseFloat(discountInput.value) || 0;

        // Validate discount percentage
        if (discountPercent < 5 || discountPercent > 50) {
            showValidationError(discountInput, 'Discount must be between 5% and 50%');
            return;
        } else {
            clearValidationError(discountInput);
        }

        // Validate original price
        if (originalPrice <= 0) {
            showValidationError(priceInput, 'Please enter a valid price first');
            return;
        } else {
            clearValidationError(priceInput);
        }

        const discountAmount = (originalPrice * discountPercent) / 100;
        const discountedPrice = originalPrice - discountAmount;

        // Update displays with animation
        discountedPriceDisplay.style.transform = 'scale(1.05)';
        discountedPriceDisplay.textContent = `KSh ${discountedPrice.toLocaleString()}`;

        if (savingsAmountDisplay) {
            savingsAmountDisplay.textContent = `KSh ${discountAmount.toLocaleString()}`;
        }

        // Reset animation
        setTimeout(() => {
            discountedPriceDisplay.style.transform = 'scale(1)';
        }, 200);

        // Show success feedback
        showValidationSuccess(discountInput, `${discountPercent}% discount applied successfully`);
    }
}

// Validation helper functions
function showValidationError(input, message) {
    clearValidationError(input);

    input.classList.add('border-red-500', 'bg-red-50');
    input.classList.remove('border-green-500', 'bg-green-50');

    const errorDiv = document.createElement('div');
    errorDiv.className = 'validation-error text-xs text-red-600 mt-1 flex items-center animate-fade-in';
    errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle mr-1"></i>${message}`;

    input.parentNode.appendChild(errorDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => clearValidationError(input), 5000);
}

function showValidationSuccess(input, message) {
    clearValidationError(input);

    input.classList.add('border-green-500', 'bg-green-50');
    input.classList.remove('border-red-500', 'bg-red-50');

    const successDiv = document.createElement('div');
    successDiv.className = 'validation-success text-xs text-green-600 mt-1 flex items-center animate-fade-in';
    successDiv.innerHTML = `<i class="fas fa-check-circle mr-1"></i>${message}`;

    input.parentNode.appendChild(successDiv);

    // Auto-remove after 3 seconds
    setTimeout(() => clearValidationError(input), 3000);
}

function clearValidationError(input) {
    const existingError = input.parentNode.querySelector('.validation-error');
    const existingSuccess = input.parentNode.querySelector('.validation-success');

    if (existingError) existingError.remove();
    if (existingSuccess) existingSuccess.remove();

    input.classList.remove('border-red-500', 'bg-red-50', 'border-green-500', 'bg-green-50');
}

function calculateExpiryDate() {
    const daysInput = document.querySelector('#hot_deal_days');
    const expiryDisplay = document.querySelector('#expiry-date-display');

    if (daysInput && expiryDisplay) {
        const days = parseInt(daysInput.value) || 7;

        // Validate days input
        if (days < 1 || days > 30) {
            showValidationError(daysInput, 'Days must be between 1 and 30');
            return;
        } else {
            clearValidationError(daysInput);
        }

        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + days);

        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };

        // Update display with animation
        expiryDisplay.style.transform = 'scale(1.05)';
        expiryDisplay.textContent = expiryDate.toLocaleDateString('en-US', options);

        // Reset animation
        setTimeout(() => {
            expiryDisplay.style.transform = 'scale(1)';
        }, 200);

        // Show success feedback
        showValidationSuccess(daysInput, `Deal expires in ${days} day${days > 1 ? 's' : ''}`);
    }
}

// Enhanced toggle hot deals section visibility with animations
function toggleHotDealsSection() {
    const hotDealCheckbox = document.querySelector('#id_is_hot_deal');
    const hotDealsSection = document.querySelector('#hot-deals-section');

    console.log('Setting up hot deals toggle', { hotDealCheckbox, hotDealsSection });

    if (hotDealCheckbox && hotDealsSection) {
        hotDealCheckbox.addEventListener('change', function() {
            console.log('Hot deal checkbox changed:', this.checked);

            if (this.checked) {
                // Show section with animation
                hotDealsSection.style.display = 'block';
                hotDealsSection.style.opacity = '0';
                hotDealsSection.style.transform = 'translateY(-10px)';

                // Trigger animation
                setTimeout(() => {
                    hotDealsSection.style.transition = 'all 0.3s ease-out';
                    hotDealsSection.style.opacity = '1';
                    hotDealsSection.style.transform = 'translateY(0)';
                }, 10);

                // Initialize calculations with delay for smooth UX
                setTimeout(() => {
                    calculateDiscountedPrice();
                    calculateExpiryDate();
                }, 300);

                // Show success toast
                showToast('Hot deal configuration enabled', 'success');

            } else {
                // Hide section with animation
                hotDealsSection.style.transition = 'all 0.3s ease-in';
                hotDealsSection.style.opacity = '0';
                hotDealsSection.style.transform = 'translateY(-10px)';

                setTimeout(() => {
                    hotDealsSection.style.display = 'none';
                }, 300);

                // Clear any validation messages
                const validationMessages = hotDealsSection.querySelectorAll('.validation-error, .validation-success');
                validationMessages.forEach(msg => msg.remove());

                // Show info toast
                showToast('Hot deal configuration disabled', 'info');
            }
        });
    }
}

// Simple toast notification function
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg text-white text-sm font-medium transform transition-all duration-300 translate-x-full opacity-0`;

    // Set colors based on type
    switch(type) {
        case 'success':
            toast.classList.add('bg-green-500');
            toast.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
            break;
        case 'error':
            toast.classList.add('bg-red-500');
            toast.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`;
            break;
        case 'warning':
            toast.classList.add('bg-yellow-500');
            toast.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${message}`;
            break;
        default:
            toast.classList.add('bg-blue-500');
            toast.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${message}`;
    }

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full', 'opacity-0');
    }, 10);

    // Auto remove after 3 seconds
    setTimeout(() => {
        toast.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

// Dynamic model loading based on brand selection
document.addEventListener('DOMContentLoaded', function() {
    const brandSelect = document.querySelector('#id_brand');
    const modelSelect = document.querySelector('#id_model');

    if (brandSelect && modelSelect) {
        brandSelect.addEventListener('change', function() {
            const brandId = this.value;

            // Clear model options
            modelSelect.innerHTML = '<option value="">---------</option>';

            if (brandId) {
                // Fetch models for selected brand
                fetch(`/api/models/${brandId}/`)
                    .then(response => response.json())
                    .then(data => {
                        data.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.id;
                            option.textContent = model.name;
                            modelSelect.appendChild(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching models:', error);
                    });
            }
        });
    }

    // Initialize hot deals functionality
    toggleHotDealsSection();

    // Initialize calculations if hot deal is already checked
    const hotDealCheckbox = document.querySelector('#id_is_hot_deal');
    if (hotDealCheckbox && hotDealCheckbox.checked) {
        calculateDiscountedPrice();
        calculateExpiryDate();
    }

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        .animate-fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-input {
            transition: all 0.3s ease;
        }

        .form-input:focus {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .enhanced-btn {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .enhanced-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .enhanced-btn:active {
            transform: translateY(0);
        }
    `;
    document.head.appendChild(style);
});

// HTMX Event Handlers for Enhanced Hot Deal Processing
function handleEditBefore(event) {
    console.log('Form submission starting...');

    // Show loading state
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i><span>Saving Changes...</span>';
    }

    // Validate hot deal fields if hot deal is enabled
    const hotDealCheckbox = document.querySelector('#id_is_hot_deal');
    if (hotDealCheckbox && hotDealCheckbox.checked) {
        const isValid = validateHotDealFields();
        if (!isValid) {
            event.preventDefault();
            showToast('Please fix hot deal validation errors', 'error');

            // Reset submit button
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i><span>Save Changes</span>';
            }
            return false;
        }
    }
}

function handleEditResponse(event) {
    console.log('Form submission completed', event.detail);

    // Reset submit button
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i><span>Save Changes</span>';
    }

    // Handle response
    if (event.detail.xhr.status === 200) {
        showToast('Car details updated successfully!', 'success');

        // Close modal after short delay
        setTimeout(() => {
            const modal = document.querySelector('.fixed.inset-0');
            if (modal) {
                modal.remove();
            }

            // Refresh the page to show updated data
            if (typeof htmx !== 'undefined') {
                htmx.trigger(document.body, 'refreshListings');
            } else {
                window.location.reload();
            }
        }, 1500);
    } else {
        showToast('Error updating car details. Please try again.', 'error');
    }
}

// Enhanced hot deal validation
function validateHotDealFields() {
    let isValid = true;

    const discountInput = document.querySelector('#hot_deal_discount');
    const daysInput = document.querySelector('#hot_deal_days');
    const priceInput = document.querySelector('#id_price');

    // Clear previous validation messages
    clearValidationError(discountInput);
    clearValidationError(daysInput);
    clearValidationError(priceInput);

    // Validate price
    const price = parseFloat(priceInput.value) || 0;
    if (price <= 0) {
        showValidationError(priceInput, 'Please enter a valid price');
        isValid = false;
    }

    // Validate discount
    const discount = parseFloat(discountInput.value) || 0;
    if (discount < 5 || discount > 50) {
        showValidationError(discountInput, 'Discount must be between 5% and 50%');
        isValid = false;
    }

    // Validate days
    const days = parseInt(daysInput.value) || 0;
    if (days < 1 || days > 30) {
        showValidationError(daysInput, 'Days must be between 1 and 30');
        isValid = false;
    }

    return isValid;
}
</script>
